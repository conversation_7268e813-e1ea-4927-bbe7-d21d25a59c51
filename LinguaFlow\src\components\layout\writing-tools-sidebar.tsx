'use client';

import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Wrench,
  FileText,
  Zap,
  CheckCircle,
  Wand2,
  Lightbulb,
  Download,
  Trash2,
  Bot,
  Shield,
  Search,
  UserCheck
} from 'lucide-react';
import { useI18n } from '@/contexts/i18n-context';

interface WritingToolsSidebarProps {
  writingMode: string;
  onWritingModeChange: (mode: string) => void;
  onFileImport: (file: File) => void;
  onCheckText: () => void;
  onAiRewrite: () => void;
  onBrainstorm: () => void;
  onExport: () => void;
  onClear: () => void;
  onPlagiarismCheck: () => void;
  onAiWritingDetection: () => void;
  onHumanizeText: () => void;
  aiPrompt: string;
  onAiPromptChange: (prompt: string) => void;
  onGenerateText: () => void;
  wordCount: number;
  charCount: number;
  errorCount: number;
  qualityScore: number;
}

export function WritingToolsSidebar({
  writingMode,
  onWritingModeChange,
  onFileImport,
  onCheckText,
  onAiRewrite,
  onBrainstorm,
  onExport,
  onClear,
  onPlagiarismCheck,
  onAiWritingDetection,
  onHumanizeText,
  aiPrompt,
  onAiPromptChange,
  onGenerateText,
  wordCount,
  charCount,
  errorCount,
  qualityScore
}: WritingToolsSidebarProps) {
  const { t } = useI18n();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileImport(file);
    }
  };

  return (
    <aside className="space-y-6">
      {/* Writing Tools Card */}
      <Card className="linguaflow-card">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center text-base" style={{ color: 'var(--text-primary)' }}>
            <Wrench className="mr-2 h-5 w-5" style={{ color: 'var(--primary-color)' }} />
            {t('writeToolsTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Writing Mode */}
          <div className="space-y-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
              {t('writingModeLabel')}
            </label>
            <Select value={writingMode} onValueChange={onWritingModeChange}>
              <SelectTrigger className="linguaflow-input">
                <SelectValue placeholder={t('selectWritingModePlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="casual">📝 {t('casualWritingMode')}</SelectItem>
                <SelectItem value="formal">👔 {t('formalWritingMode')}</SelectItem>
                <SelectItem value="academic">🎓 {t('academicWritingMode')}</SelectItem>
                <SelectItem value="creative">🎨 {t('creativeWritingMode')}</SelectItem>
                <SelectItem value="business">💼 {t('businessWritingMode')}</SelectItem>
                <SelectItem value="technical">⚙️ {t('technicalWritingMode')}</SelectItem>
                <SelectItem value="professional">💼 {t('professionalWritingMode')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Import Document */}
          <div className="space-y-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
              {t('importDocumentTitle')}
            </label>
            <div 
              className="border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:border-emerald-400"
              style={{ borderColor: 'var(--border-color)' }}
              onClick={() => document.getElementById('fileInput')?.click()}
            >
              <FileText className="mx-auto h-8 w-8 mb-2" style={{ color: 'var(--text-secondary)' }} />
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                {t('dropzoneInstruction')}
              </p>
              <input
                type="file"
                id="fileInput"
                className="hidden"
                accept=".txt,.md,.docx"
                onChange={handleFileChange}
              />
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
              {t('quickActionTitle')}
            </label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={onCheckText}
                className="linguaflow-button-secondary"
              >
                <CheckCircle className="mr-1 h-4 w-4" />
                Check Text
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onAiRewrite}
                className="linguaflow-button-secondary"
              >
                <Wand2 className="mr-1 h-4 w-4" />
                AI Rewrite ✨
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onBrainstorm}
                className="linguaflow-button-secondary"
              >
                <Lightbulb className="mr-1 h-4 w-4" />
                Brainstorm ✨
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onPlagiarismCheck}
                className="linguaflow-button-secondary"
              >
                <Shield className="mr-1 h-4 w-4" />
                Plagiarism
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onAiWritingDetection}
                className="linguaflow-button-secondary"
              >
                <Search className="mr-1 h-4 w-4" />
                AI Detection
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onHumanizeText}
                className="linguaflow-button-secondary"
              >
                <UserCheck className="mr-1 h-4 w-4" />
                Humanize ✨
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onExport}
                className="linguaflow-button-secondary"
              >
                <Download className="mr-1 h-4 w-4" />
                Export
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={onClear}
                className="linguaflow-button-secondary"
              >
                <Trash2 className="mr-1 h-4 w-4" />
                {t('clearEditorButton')}
              </Button>
            </div>
          </div>

          {/* AI Text Generation */}
          <div className="space-y-2">
            <label className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
              AI Text Generation ✨
            </label>
            <Textarea
              value={aiPrompt}
              onChange={(e) => onAiPromptChange(e.target.value)}
              rows={2}
              className="linguaflow-input"
              placeholder={t('promptPlaceholder')}
            />
            <Button
              onClick={onGenerateText}
              className="linguaflow-button w-full"
              size="sm"
            >
              <Bot className="mr-1 h-4 w-4" />
              Generate with AI ✨
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Writing Statistics Card */}
      <Card className="linguaflow-stats">
        <CardContent className="p-4">
          <h4 className="font-semibold text-center mb-3 text-white">
            {t('writingStatsTitle')}
          </h4>
          <div className="space-y-2 text-sm text-white">
            <div className="flex justify-between items-center">
              <span>{t('wordCountLabel')}:</span>
              <span className="font-medium">{wordCount}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>{t('charCountLabel')}:</span>
              <span className="font-medium">{charCount}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Issues:</span>
              <span className="font-medium">{errorCount}</span>
            </div>
            <div className="flex justify-between items-center">
              <span>Score:</span>
              <span className="font-medium text-lg">{qualityScore}%</span>
            </div>
          </div>
          <div className="mt-3 linguaflow-progress">
            <div 
              className="linguaflow-progress-bar"
              style={{ width: `${qualityScore}%` }}
            />
          </div>
        </CardContent>
      </Card>
    </aside>
  );
}
