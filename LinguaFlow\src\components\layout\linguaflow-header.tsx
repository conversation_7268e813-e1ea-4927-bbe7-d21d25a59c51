'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Moon, Sun, HelpCircle } from 'lucide-react';
import { Logo } from '@/components/ui/logo';
import { useI18n } from '@/contexts/i18n-context';
import { useAppearance } from '@/contexts/theme-context';
import { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';

interface LinguaFlowHeaderProps {
  onSettingsClick: () => void;
  onHelpClick: () => void;
}

export function LinguaFlowHeader({ onSettingsClick, onHelpClick }: LinguaFlowHeaderProps) {
  const { t, uiLanguage, setUiLanguage } = useI18n();
  const { theme, setTheme, effectiveTheme } = useAppearance();
  const isRTL = uiLanguage === 'ar';

  return (
    <header className="sticky top-0 z-50 linguaflow-header shadow-sm">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`flex justify-between items-center h-16 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* Logo and Title */}
          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
            <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
              <Logo className="h-8 w-8" />
              <h1 
                className="text-xl font-bold" 
                style={{ color: 'var(--text-primary)' }}
              >
                {t('appName')}
              </h1>
            </div>
            
            {/* Language Selector - Hidden on mobile */}
            <div className={`hidden md:flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
              <span 
                className="text-sm" 
                style={{ color: 'var(--text-secondary)' }}
              >
                {t('languageLabel')}:
              </span>
              <Select value={uiLanguage} onValueChange={setUiLanguage}>
                <SelectTrigger 
                  className="w-auto min-w-[120px] h-8 text-sm linguaflow-input"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-color)',
                    color: 'var(--text-primary)'
                  }}
                >
                  <SelectValue placeholder={t('selectLanguagePlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {t(lang.labelKey)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`flex items-center space-x-2 sm:space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
            <Button
              variant="ghost"
              size="icon"
              onClick={onSettingsClick}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'var(--bg-alt)'
              }}
              aria-label={t('settingsTitle')}
            >
              <Settings className="h-5 w-5" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(effectiveTheme === 'dark' ? 'light' : 'dark')}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'var(--bg-alt)'
              }}
              aria-label={effectiveTheme === 'dark' ? t('switchToLightMode') : t('switchToDarkMode')}
            >
              {effectiveTheme === 'dark' ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={onHelpClick}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
              style={{ 
                color: 'var(--text-secondary)',
                backgroundColor: 'var(--bg-alt)'
              }}
              aria-label={t('helpTitle')}
            >
              <HelpCircle className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
